# 收藏同步功能测试指南

## 测试目标
验证收藏同步优化方案的效果，确保：
1. 减少云函数调用次数
2. 收藏状态在各页面间实时同步
3. 缓存机制正常工作

## 测试场景

### 场景1：Detail页面收藏状态缓存测试
**步骤：**
1. 打开supply或demand的detail页面
2. 点击收藏按钮进行收藏
3. 返回列表页面，再次进入同一个detail页面
4. 观察收藏状态是否正确显示（应该从缓存获取，不调用云函数）

**预期结果：**
- 收藏状态正确显示
- 控制台显示"缓存命中"相关日志
- 减少云函数调用

### 场景2：页面间收藏状态同步测试
**步骤：**
1. 打开supply detail页面，点击收藏
2. 不关闭detail页面，切换到myCollection页面
3. 在myCollection页面删除刚才的收藏
4. 返回detail页面，观察收藏按钮状态

**预期结果：**
- Detail页面的收藏按钮状态自动更新为未收藏
- 无需手动刷新页面

### 场景3：myCollection删除同步测试
**步骤：**
1. 在myCollection页面删除一个收藏项
2. 如果对应的detail页面在页面栈中，检查其收藏状态
3. 重新进入该detail页面，确认收藏状态

**预期结果：**
- 删除操作成功
- Detail页面收藏状态同步更新
- 收藏列表正确移除项目

### 场景4：缓存失效测试
**步骤：**
1. 在一个页面收藏某项目
2. 等待缓存过期时间（5分钟）
3. 在另一个页面查看该项目的收藏状态

**预期结果：**
- 缓存过期后重新查询云函数
- 收藏状态仍然正确

## 性能监控

### 云函数调用监控
在微信开发者工具的控制台中观察：
- `collectionManager` 云函数的调用频率
- 缓存命中率
- 页面切换时的网络请求数量

### 预期改进效果
- 云函数调用减少 60-80%
- 页面响应速度提升
- 用户体验更流畅

## 调试技巧

### 查看缓存状态
在控制台执行：
```javascript
// 查看当前缓存状态
console.log('收藏缓存:', collectionUtils.collectionCache);
console.log('单个查询缓存:', collectionUtils.singleQueryCache);
```

### 清空缓存测试
```javascript
// 清空缓存进行测试
collectionUtils.collectionCache.supply.clear();
collectionUtils.collectionCache.demand.clear();
collectionUtils.singleQueryCache.clear();
```

### 模拟缓存过期
```javascript
// 设置缓存为过期状态
collectionUtils.collectionCache.lastUpdate = Date.now() - (6 * 60 * 1000);
```

## 常见问题排查

### 问题1：收藏状态不同步
**可能原因：**
- 页面通信机制失效
- 缓存更新不及时

**排查方法：**
- 检查 `notifyCollectionStatusChange` 方法是否被调用
- 确认页面栈中是否存在目标页面

### 问题2：缓存不生效
**可能原因：**
- 缓存键值不匹配
- 缓存过期时间设置问题

**排查方法：**
- 检查 `getCachedCollectionStatus` 方法的返回值
- 验证缓存键值的生成逻辑

### 问题3：云函数调用仍然频繁
**可能原因：**
- 缓存未正确命中
- 页面生命周期处理有误

**排查方法：**
- 添加日志跟踪缓存命中情况
- 检查 `onShow` 方法的调用逻辑

## 测试完成标准
- [ ] 所有测试场景通过
- [ ] 云函数调用次数明显减少
- [ ] 收藏状态同步正常
- [ ] 无明显性能问题
- [ ] 用户体验良好
