// 引入图片处理工具
const imageUtils = require('../../../supply/publish/imageUtils.js');
// 引入表单处理工具
const formUtils = require('../../../supply/publish/formUtils.js');
// 引入云数据库
const db = wx.cloud.database();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 帖子ID
    postId: '',
    // 帖子数据
    postData: null,
    // 图片列表
    imageList: [],
    // 原始图片列表（用于比较变化）
    originalImageList: [],
    // 已删除的图片列表（用于从云存储中删除）
    deletedImages: [],
    // 是否正在提交
    submitting: false,
    // 表单验证错误
    errors: {},
    // 表单数据
    formData: {
      title: '',           // 植物名称
      price: '',           // 上车价
      trunkDiameter: '',   // 米径
      chestDiameter: '',   // 胸径
      groundDiameter: '',  // 地径
      cupSize: '',         // 杯口
      crownWidth: '',      // 冠幅
      height: '',          // 高度
      branchPoint: '',     // 分支
      clumpCount: '',      // 丛生
      clumpDiameter: '',   // 杆径
      quantity: '',        // 数量
      unit: '',            // 单位（不显示给用户编辑）
      quality: '',         // 产品质量
      content: ''          // 简介
    },
    // 产品质量选项
    qualityOptions: ['精品', '中等', '一般'],
    // 数量单位
    quantityUnit: '',
    // 底部安全区域高度
    safeBottomHeight: 0,
    // Canvas上下文
    ctx: null,
    // 位置信息
    locationInfo: null,
    // 是否拍照上传过新照片
    hasNewPhotos: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    if (!options.id) {
      this.showError('缺少必要的帖子ID参数');
      return;
    }

    this.setData({ 
      postId: options.id,
      deletedImages: [], // 初始化已删除图片数组
      hasNewPhotos: false // 初始化拍照上传标志
    });
    
    // 设置底部安全区域高度
    this.setSafeBottomHeight();
    
    // 加载帖子数据
    this.loadPostData();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {
    // 初始化Canvas
    this.initCanvas();
  },

  /**
   * 初始化Canvas用于添加水印
   */
  initCanvas: function() {
    const query = wx.createSelectorQuery();
    query.select('#watermark-canvas')
      .fields({ node: true, size: true })
      .exec((res) => {
        if (res[0]) {
          const canvas = res[0].node;
          this.setData({ canvas: canvas });
        }
      });
  },

  /**
   * 设置底部安全区域高度
   */
  setSafeBottomHeight: function() {
    try {
      // 使用新API替换已弃用的wx.getSystemInfoSync()
      const windowInfo = wx.getWindowInfo();
      // 检测是否有底部安全区域（如iPhone X系列）
      let safeBottomHeight = 60; // 默认高度
      
      if (windowInfo.safeArea) {
        const safeAreaBottom = windowInfo.safeArea.bottom;
        const screenHeight = windowInfo.screenHeight;
        // 如果安全区域底部与屏幕底部有差距，说明有底部安全区域
        if (screenHeight > safeAreaBottom) {
          safeBottomHeight = (screenHeight - safeAreaBottom) * 2; // 转为rpx
        }
      }
      
      this.setData({ safeBottomHeight });
    } catch (e) {
      console.error('获取系统信息失败', e);
      // 使用默认值
      this.setData({ safeBottomHeight: 60 });
    }
  },

  /**
   * 加载帖子数据
   */
  loadPostData: function() {
    wx.showLoading({
      title: '加载中...',
      mask: true
    });

    // 调用云函数获取帖子详情
    wx.cloud.callFunction({
      name: 'quickstartFunctions',
      data: {
        type: 'getDemandDetail',
        id: this.data.postId
      },
      success: res => {
        wx.hideLoading();
        
        if (res.result && res.result.code === 0) {
          const postData = res.result.data;
          
          if (!postData) {
            this.showError('未找到帖子数据');
            return;
          }
          
          // 设置帖子数据，但不处理图片
          this.setData({
            postData: postData,
            imageList: postData.imageList || [],
            originalImageList: [...(postData.imageList || [])]
          });
          
          // 处理表单数据
          this.processFormData(postData);
        } else {
          this.showError(res.result?.msg || '获取帖子数据失败');
        }
      },
      fail: err => {
        wx.hideLoading();
        console.error('获取帖子数据失败:', err);
        this.showError('网络异常，请重试');
      }
    });
  },

  /**
   * 处理表单数据
   */
  processFormData: function(postData) {
    // 创建表单数据对象
    const formData = {
      title: postData.title || '',
      price: postData.price || '',
      trunkDiameter: postData.meter_diameter || '',
      chestDiameter: postData.thorax_diameter || '',
      groundDiameter: postData.ground_diameter || '',
      cupSize: postData.cup || '',
      crownWidth: postData.canopy || '',
      height: postData.height || '',
      branchPoint: postData.branchPos || '',
      clumpCount: postData.clumpCount || '',
      clumpDiameter: postData.clumpDiameter || '',
      quantity: postData.quantity || '',
      unit: postData.unit || '',
      quality: postData.quality || '',
      content: postData.content || ''
    };

    // 更新数据
    this.setData({
      formData,
      quantityUnit: postData.unit || ''
    });
  },

  /**
   * 处理输入变化
   */
  onInputChange: function(e) {
    const { field } = e.currentTarget.dataset;
    const value = e.detail.value;

    this.setData({
      [`formData.${field}`]: value
    });
  },

  /**
   * 处理产品质量选择
   */
  onQualityChange: function(e) {
    this.setData({
      'formData.quality': e.detail.value
    });
  },

  /**
   * 显示错误信息
   */
  showError: function(msg) {
    wx.showToast({
      title: msg,
      icon: 'none',
      duration: 2000
    });
  },

  /**
   * 添加图片 - 禁用
   */
  addImage: function() {
    // 禁用图片上传功能
    wx.showToast({
      title: '求购信息不支持修改图片',
      icon: 'none',
      duration: 2000
    });
      return;
  },

  /**
   * 拍照上传
   */
  takePhoto: function() {
    if (this.data.imageList.length >= 6) {
      this.showError('最多只能上传6张图片');
      return;
    }

    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['camera'],
      camera: 'back',
      success: async res => {
        const tempFiles = res.tempFiles;
        
        if (tempFiles && tempFiles.length > 0) {
          wx.showLoading({
            title: '处理图片中...',
            mask: true
          });
          
          try {
            // 添加水印
            const watermarkedImages = await imageUtils.addWatermarkToImages([tempFiles[0].tempFilePath]);
            
            // 压缩图片
            const compressedImages = await imageUtils.compressImages(watermarkedImages, 0.8);
            
            // 更新图片列表
            const newImageList = [...this.data.imageList, ...compressedImages];
            this.setData({
              imageList: newImageList,
              hasNewPhotos: true // 标记已拍照上传新照片
            });
            
            wx.hideLoading();
            
            // 拍照成功后获取高精度位置信息
            this.getHighAccuracyLocation();
          } catch (error) {
            wx.hideLoading();
            console.error('处理图片失败:', error);
            this.showError('处理图片失败，请重试');
          }
        }
      }
    });
  },

  /**
   * 获取高精度位置信息
   */
  getHighAccuracyLocation: function() {
    wx.showLoading({
      title: '获取位置中...',
      mask: true
    });
    
    wx.getLocation({
      type: 'gcj02', // 使用国测局坐标系
      isHighAccuracy: true, // 开启高精度定位
      highAccuracyExpireTime: 3000, // 高精度定位超时时间，单位ms
      success: res => {
        console.log('获取位置成功:', res);
        // 保存位置信息到data中
        this.setData({
          'locationInfo': {
            latitude: res.latitude,
            longitude: res.longitude,
            accuracy: res.accuracy,
            speed: res.speed,
            altitude: res.altitude,
            verticalAccuracy: res.verticalAccuracy,
            horizontalAccuracy: res.horizontalAccuracy
          }
        });
        
        // 显示成功提示
        wx.showToast({
          title: '位置已更新',
          icon: 'success',
          duration: 1500
        });
      },
      fail: err => {
        console.error('获取位置失败:', err);
        wx.showModal({
          title: '位置获取失败',
          content: '无法获取您的当前位置，请确保已授权位置权限并处于网络良好的环境',
          showCancel: false
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  /**
   * 从相册选择
   */
  chooseImage: function() {
    const remainCount = 6 - this.data.imageList.length;
    if (remainCount <= 0) {
      this.showError('最多只能上传6张图片');
      return;
    }

    wx.chooseMedia({
      count: remainCount,
      mediaType: ['image'],
      sourceType: ['album'],
      success: async res => {
        const tempFiles = res.tempFiles;
        
        if (tempFiles && tempFiles.length > 0) {
          wx.showLoading({
            title: '处理图片中...',
            mask: true
          });
          
          try {
            const tempFilePaths = tempFiles.map(file => file.tempFilePath);
            
            // 添加水印
            const watermarkedImages = await imageUtils.addWatermarkToImages(tempFilePaths);
            
            // 压缩图片
            const compressedImages = await imageUtils.compressImages(watermarkedImages, 0.8);
            
            // 更新图片列表
            const newImageList = [...this.data.imageList, ...compressedImages];
            this.setData({
              imageList: newImageList
              // 注意：从相册选择不计入拍照上传，不设置hasNewPhotos
            });
            
            wx.hideLoading();
          } catch (error) {
            wx.hideLoading();
            console.error('处理图片失败:', error);
            this.showError('处理图片失败，请重试');
          }
        }
      }
    });
  },

  /**
   * 预览图片
   */
  previewImage: function(e) {
    const index = e.currentTarget.dataset.index;
    const images = this.data.imageList;
    
    // 强制刷新当前图片
    this.refreshSingleImage(index);
    
    // 延迟一下再预览，确保图片已刷新
    setTimeout(() => {
      wx.previewImage({
        current: images[index],
        urls: images
      });
    }, 100);
  },
  
  /**
   * 刷新单个图片
   */
  refreshSingleImage: function(index) {
    if (index === undefined || !this.data.imageList[index]) return;
    
    // 获取当前图片URL
    const currentUrl = this.data.imageList[index];
    const imageList = [...this.data.imageList];
    
    // 先设置为空，然后再恢复原URL，强制刷新
    imageList[index] = '';
    
    this.setData({
      imageList: imageList
    }, () => {
      // 短暂延迟后恢复原URL
      setTimeout(() => {
        imageList[index] = currentUrl;
        this.setData({
          imageList: imageList
        });
      }, 50);
    });
  },

  /**
   * 删除图片 - 禁用
   */
  deleteImage: function(e) {
    // 禁用图片删除功能
    wx.showToast({
      title: '求购信息不支持修改图片',
      icon: 'none',
      duration: 2000
    });
    return;
  },



  /**
   * 保存修改
   */
  saveChanges: function() {
    wx.showModal({
      title: '确认保存',
      content: '确定要保存修改吗？',
      success: res => {
        if (res.confirm) {
          this.submitChanges();
        }
      }
    });
  },

  /**
   * 提交修改
   */
  submitChanges: function() {
    const { postId, formData, postData } = this.data;

    if (this.data.submitting) {
      return;
    }

    this.setData({ submitting: true });

    wx.showLoading({
      title: '保存中...',
      mask: true
    });

    // 构建更新数据
    const updateData = {};

    // 添加表单数据
    // 植物名称
    updateData.title = formData.title;

    // 价格相关
    updateData.price = formData.price !== '' ? parseFloat(formData.price) : 0;

    // 规格参数
    updateData.meter_diameter = formData.trunkDiameter !== '' ? parseFloat(formData.trunkDiameter) : null;
    updateData.thorax_diameter = formData.chestDiameter !== '' ? parseFloat(formData.chestDiameter) : null;
    updateData.ground_diameter = formData.groundDiameter !== '' ? parseFloat(formData.groundDiameter) : null;
    updateData.cup = formData.cupSize !== '' ? parseFloat(formData.cupSize) : null;
    updateData.canopy = formData.crownWidth !== '' ? parseFloat(formData.crownWidth) : null;
    updateData.height = formData.height !== '' ? parseFloat(formData.height) : null;
    updateData.branchPos = formData.branchPoint !== '' ? parseFloat(formData.branchPoint) : null;
    updateData.clumpCount = formData.clumpCount !== '' ? parseFloat(formData.clumpCount) : null;
    updateData.clumpDiameter = formData.clumpDiameter !== '' ? parseFloat(formData.clumpDiameter) : null;

    // 数量和其他
    updateData.quantity = formData.quantity;
    updateData.unit = formData.unit;
    updateData.quality = formData.quality;
    updateData.content = formData.content;
    
    // 保留原始图片列表，不允许修改
    updateData.imageList = this.data.originalImageList;
        
        // 更新帖子数据
    wx.cloud.callFunction({
          name: 'quickstartFunctions',
          data: {
        type: 'updateDemand',
            id: postId,
            updateData: updateData
          }
      })
      .then(res => {
        this.setData({ submitting: false });
        wx.hideLoading();
        
        if (res.result && res.result.code === 0) {
          wx.showToast({
            title: '更新成功',
            icon: 'success',
            duration: 2000
          });
          
          // 返回上一页
          setTimeout(() => {
            wx.navigateBack();
          }, 2000);
        } else {
          console.error('更新失败，服务器返回:', res.result);
          // 显示具体错误信息
          const errorMsg = res.result?.msg || '更新失败，请重试';
          this.showError(errorMsg);
        }
      })
      .catch(err => {
        this.setData({ submitting: false });
        wx.hideLoading();
        console.error('更新失败:', err);
        
        // 尝试提取更详细的错误信息
        let errorMsg = '更新失败，请重试';
        if (err && err.errMsg) {
          errorMsg = err.errMsg;
        } else if (err && typeof err === 'object') {
          errorMsg = JSON.stringify(err).substr(0, 100);
        } else if (err && typeof err === 'string') {
          errorMsg = err;
        }
        
        this.showError(errorMsg);
      });
  },

  /**
   * 强制刷新图片
   */
  refreshImages: function() {
    // 获取当前图片列表
    const currentImages = this.data.imageList;
    
    // 先清空图片列表，然后重新设置，强制刷新
    this.setData({ imageList: [] }, () => {
      setTimeout(() => {
        this.setData({ imageList: currentImages });
      }, 50);
    });
  },

  /**
   * 上传图片
   */
  uploadImages: function(postId, imageList) {
    return new Promise(async (resolve, reject) => {
      try {
        const app = getApp();
        const userId = app.globalData.userId || '';
        
        if (!userId) {
          reject(new Error('未获取到用户ID'));
          return;
        }
        
        // 如果没有图片，直接返回空数组
        if (!imageList || imageList.length === 0) {
          resolve([]);
          return;
        }
        
        wx.showLoading({
          title: '上传图片中...',
          mask: true
        });
        
        // 创建文件夹路径 - 与publish.js保持一致
        const folderPath = `demand_images/${userId}_${postId}`;
        
        const uploadTasks = imageList.map((filePath, index) => {
          // 检查是否是已有的云存储图片（以cloud://开头）
          if (filePath.startsWith('cloud://')) {
            return Promise.resolve(filePath);
          }
          
          // 获取文件扩展名
          const extension = filePath.match(/\.([^.]+)$/)?.[1] || 'jpg';
          
          // 生成云存储路径，与publish.js保持一致
          const cloudPath = `${folderPath}/image_${index}.${extension}`;
          
          return wx.cloud.uploadFile({
            cloudPath: cloudPath,
            filePath: filePath
          }).then(res => res.fileID);
        });
        
        const uploadResults = await Promise.all(uploadTasks);

        wx.hideLoading();
        resolve(uploadResults);
      } catch (error) {
        wx.hideLoading();
        console.error('上传图片失败:', error);
        reject(error);
      }
    });
  },



  /**
   * 返回上一页
   */
  goBack: function() {
    // 直接调用onNavBack方法，确保返回逻辑一致
    this.onNavBack();
  },

  /**
   * 导航栏返回按钮点击事件
   */
  onNavBack: function(e) {
    // 检查是否有删除或新增的照片
    const hasImageChanges = this.data.deletedImages.length > 0 || 
                           (this.data.imageList.length !== this.data.originalImageList.length);
    
    // 检查是否有修改规格参数
    let hasSpecChanges = false;
    const { specs, postData } = this.data;
    for (const key in specs) {
      if (specs[key].visible && String(specs[key].value) !== String(postData[key] || '')) {
        hasSpecChanges = true;
        break;
      } else if (!specs[key].visible && postData[key]) {
        hasSpecChanges = true;
        break;
      }
    }
    
    // 如果有任何修改，提示用户确认
    if (hasImageChanges || hasSpecChanges) {
      wx.showModal({
        title: '未保存的修改',
        content: '您有未保存的修改，确定要返回吗？',
        success: res => {
          if (res.confirm) {
            wx.navigateBack();
          }
          // 如果用户点击取消，则不执行任何操作
        }
      });
    } else {
      wx.navigateBack();
    }
  },

  /**
   * 导航栏首页按钮点击事件
   */
  onNavHome: function() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    });
  },

  /**
   * 从云存储中删除图片
   */
  deleteCloudImages: function(imageList) {
    // 如果没有要删除的图片，直接返回成功
    if (!imageList || imageList.length === 0) {
      return Promise.resolve();
    }
    
    console.log('删除云存储图片:', imageList);
    
    // 创建删除任务列表
    const deletePromises = imageList.map(fileID => {
      return wx.cloud.deleteFile({
        fileList: [fileID]
      })
      .then(res => {
        console.log('删除云存储图片成功:', res);
        return res;
      })
      .catch(err => {
        console.error('删除云存储图片失败:', err);
        // 即使删除失败，也继续执行流程
        return Promise.resolve();
      });
    });
    
    // 等待所有删除任务完成
    return Promise.all(deletePromises);
  },

  /**
   * 处理图片加载错误
   */
  handleImageError: function(e) {
    const idx = e.currentTarget.dataset.idx;
    if (idx === undefined || !this.data.imageList[idx]) return;
    
    console.log('图片加载错误，尝试重新加载:', idx);
    
    // 获取当前图片URL
    const currentUrl = this.data.imageList[idx];
    
    // 使用延迟重新设置图片URL，强制刷新
    const imageList = [...this.data.imageList];
    
    // 先设置为空，然后再恢复原URL，强制刷新
    imageList[idx] = '';
    
    this.setData({
      imageList: imageList
    }, () => {
      // 短暂延迟后恢复原URL
      setTimeout(() => {
        imageList[idx] = currentUrl;
        this.setData({
          imageList: imageList
        });
      }, 100);
    });
  }
}); 