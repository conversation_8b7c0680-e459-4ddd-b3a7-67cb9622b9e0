// 引入图片处理工具
const imageUtils = require('../../../supply/publish/imageUtils.js');
// 引入表单处理工具
const formUtils = require('../../../supply/publish/formUtils.js');
// 引入云数据库
const db = wx.cloud.database();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 帖子ID
    postId: '',
    // 帖子数据
    postData: null,
    // 图片列表（老数据格式）
    imageList: [],
    // 新图片列表（新数据格式，包含拍照时间）
    newImageList: [],
    // 原始图片列表（用于比较变化）
    originalImageList: [],
    // 原始新图片列表（用于比较变化）
    originalNewImageList: [],
    // 已删除的图片列表（用于从云存储中删除）
    deletedImages: [],
    // 已删除的新图片列表（用于从云存储中删除）
    deletedNewImages: [],
    // 是否正在提交
    submitting: false,
    // 表单验证错误
    errors: {},
    // 表单数据
    formData: {
      title: '',           // 植物名称
      price: '',           // 上车价
      trunkDiameter: '',   // 米径
      chestDiameter: '',   // 胸径
      groundDiameter: '',  // 地径
      cupSize: '',         // 杯口
      crownWidth: '',      // 冠幅
      height: '',          // 高度
      branchPoint: '',     // 分支
      clumpCount: '',      // 丛生
      clumpDiameter: '',   // 杆径
      quantity: '',        // 数量
      quality: '',         // 产品质量
      content: ''          // 简介
    },
    // 产品质量选项
    qualityOptions: ['精品', '中等', '一般'],
    // 价格单位
    priceUnit: '',
    // 底部安全区域高度
    safeBottomHeight: 0,
    // Canvas上下文
    ctx: null,
    // 位置信息
    locationInfo: null,
    // 是否拍照上传过新照片
    hasNewPhotos: false,
    // 是否拍摄了新照片（用于isTakePhoto字段）
    hasTakenNewPhoto: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    if (!options.id) {
      this.showError('缺少必要的帖子ID参数');
      return;
    }

    this.setData({
      postId: options.id,
      deletedImages: [], // 初始化已删除图片数组
      hasNewPhotos: false, // 初始化拍照上传标志
      hasTakenNewPhoto: false // 初始化拍摄新照片标志
    });
    
    // 设置底部安全区域高度
    this.setSafeBottomHeight();
    
    // 加载帖子数据
    this.loadPostData();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {
    // 初始化Canvas
    this.initCanvas();
  },

  /**
   * 初始化Canvas用于添加水印
   */
  initCanvas: function() {
    const query = wx.createSelectorQuery();
    query.select('#watermark-canvas')
      .fields({ node: true, size: true })
      .exec((res) => {
        if (res[0]) {
          const canvas = res[0].node;
          this.setData({ canvas: canvas });
        }
      });
  },

  /**
   * 设置底部安全区域高度
   */
  setSafeBottomHeight: function() {
    try {
      // 使用新API替换已弃用的wx.getSystemInfoSync()
      const windowInfo = wx.getWindowInfo();
      // 检测是否有底部安全区域（如iPhone X系列）
      let safeBottomHeight = 60; // 默认高度
      
      if (windowInfo.safeArea) {
        const safeAreaBottom = windowInfo.safeArea.bottom;
        const screenHeight = windowInfo.screenHeight;
        // 如果安全区域底部与屏幕底部有差距，说明有底部安全区域
        if (screenHeight > safeAreaBottom) {
          safeBottomHeight = (screenHeight - safeAreaBottom) * 2; // 转为rpx
        }
      }
      
      this.setData({ safeBottomHeight });
    } catch (e) {
      console.error('获取系统信息失败', e);
      // 使用默认值
      this.setData({ safeBottomHeight: 60 });
    }
  },

  /**
   * 加载帖子数据
   */
  loadPostData: function() {
    wx.showLoading({
      title: '加载中...',
      mask: true
    });

    // 调用云函数获取帖子详情
    wx.cloud.callFunction({
      name: 'quickstartFunctions',
      data: {
        type: 'getSupplyDetail',
        id: this.data.postId
      },
      success: res => {
        wx.hideLoading();
        
        if (res.result && res.result.code === 0) {
          const postData = res.result.data;
          
          if (!postData) {
            this.showError('未找到帖子数据');
            return;
          }
          
          // 设置帖子数据
          this.setData({
            postData: postData,
            imageList: postData.imageList || [],
            newImageList: postData.newImageList || [],
            originalImageList: [...(postData.imageList || [])],
            originalNewImageList: [...(postData.newImageList || [])]
          }, () => {
            // 数据加载完成后，进行数据一致性检查
            this.checkDataConsistency();

            // 延迟一段时间后刷新图片
            setTimeout(() => {
              this.refreshImages();
            }, 200);
          });
          
          // 处理表单数据
          this.processFormData(postData);
        } else {
          this.showError(res.result?.msg || '获取帖子数据失败');
        }
      },
      fail: err => {
        wx.hideLoading();
        console.error('获取帖子数据失败:', err);
        this.showError('网络异常，请重试');
      }
    });
  },

  /**
   * 处理表单数据
   */
  processFormData: function(postData) {
    // 创建表单数据对象
    const formData = {
      title: postData.title || '',
      price: postData.price || '',
      trunkDiameter: postData.meter_diameter || '',
      chestDiameter: postData.thorax_diameter || '',
      groundDiameter: postData.ground_diameter || '',
      cupSize: postData.cup || '',
      crownWidth: postData.canopy || '',
      height: postData.height || '',
      branchPoint: postData.branchPos || '',
      clumpCount: postData.clumpCount || '',
      clumpDiameter: postData.clumpDiameter || '',
      quantity: postData.quantity || '',
      quality: postData.quality || '',
      content: postData.content || ''
    };

    // 更新数据
    this.setData({
      formData,
      priceUnit: postData.price_unit || ''
    });
  },

  /**
   * 处理输入变化
   */
  onInputChange: function(e) {
    const { field } = e.currentTarget.dataset;
    const value = e.detail.value;

    this.setData({
      [`formData.${field}`]: value
    });
  },

  /**
   * 处理产品质量选择
   */
  onQualityChange: function(e) {
    this.setData({
      'formData.quality': e.detail.value
    });
  },

  /**
   * 上传新图片（使用新的文件命名规则）
   */
  uploadNewImages: function(postId, newImageList) {
    return new Promise(async (resolve, reject) => {
      try {
        const app = getApp();
        const userId = app.globalData.userId || '';

        if (!userId) {
          reject(new Error('未获取到用户ID'));
          return;
        }

        // 如果没有新图片，直接返回空数组
        if (!newImageList || newImageList.length === 0) {
          resolve([]);
          return;
        }

        wx.showLoading({
          title: '上传新图片中...',
          mask: true
        });

        // 创建文件夹路径
        const folderPath = `supply_images/${userId}_${postId}`;

        const uploadTasks = newImageList.map((imageItem, index) => {
          // 检查是否是已有的云存储图片（以cloud://开头）
          if (imageItem.url.startsWith('cloud://')) {
            return Promise.resolve({
              url: imageItem.url,
              captureTime: imageItem.captureTime,
              uniqueId: imageItem.uniqueId
              // 注意：不包含isNewInCurrentSession，因为已存在的图片不应该显示"新"标签
            });
          }

          // 获取文件扩展名
          const extension = imageItem.url.match(/\.([^.]+)$/)?.[1] || 'jpg';

          // 使用uniqueId生成唯一文件名，避免命名冲突
          const fileId = imageItem.uniqueId || `fallback_${Date.now()}_${index}`;
          const cloudPath = `${folderPath}/imageNew_${fileId}.${extension}`;

          return wx.cloud.uploadFile({
            cloudPath: cloudPath,
            filePath: imageItem.url
          }).then(res => {
            if (!res.fileID) {
              throw new Error(`图片上传失败：${cloudPath}`);
            }
            console.log(`图片上传成功：${cloudPath} -> ${res.fileID}`);
            return {
              url: res.fileID,
              captureTime: imageItem.captureTime,
              uniqueId: imageItem.uniqueId
              // 注意：不包含isNewInCurrentSession，因为上传后就不再是"新"的了
            };
          }).catch(uploadError => {
            console.error(`图片上传失败：${cloudPath}`, uploadError);
            throw new Error(`图片上传失败：${cloudPath}，错误：${uploadError.errMsg || uploadError.message}`);
          });
        });

        const uploadResults = await Promise.all(uploadTasks);

        wx.hideLoading();
        resolve(uploadResults);
      } catch (error) {
        wx.hideLoading();
        console.error('上传新图片失败:', error);
        reject(error);
      }
    });
  },

  /**
   * 安全上传图片（保险操作）
   */
  uploadImagesWithSafety: function(postId, imageList, newImageList, deletedImages, deletedNewImages, updateData) {
    return new Promise((resolve, reject) => {
      // 第一步：上传新图片，确保文件上传成功
      Promise.all([
        this.uploadImages(postId, imageList),
        this.uploadNewImages(postId, newImageList)
      ])
        .then(([imageUrls, newImageUrls]) => {
          console.log('图片上传成功，开始更新数据库');

          // 第二步：上传成功后，设置图片列表
          updateData.imageList = imageUrls;
          updateData.newImageList = newImageUrls;

          // 第三步：更新数据库
          return wx.cloud.callFunction({
            name: 'quickstartFunctions',
            data: {
              type: 'updateSupply',
              id: postId,
              updateData: updateData
            }
          });
        })
        .then(result => {
          console.log('数据库更新成功，开始清理旧文件');

          // 第四步：数据库更新成功后，才删除旧文件
          return Promise.all([
            this.deleteCloudImages(deletedImages),
            this.deleteCloudImages(deletedNewImages)
          ]).then(() => {
            console.log('旧文件清理完成');
            return result; // 返回数据库更新结果
          }).catch(deleteError => {
            console.warn('旧文件删除失败，但不影响主流程:', deleteError);
            return result; // 即使删除失败也返回成功，因为主要操作已完成
          });
        })
        .then(result => {
          resolve(result);
        })
        .catch(error => {
          console.error('上传或更新过程中出现错误:', error);

          // 如果是上传阶段失败，不需要回滚（因为还没更新数据库）
          // 如果是数据库更新失败，新上传的文件会成为"孤儿文件"，但不影响用户数据一致性
          reject(error);
        });
    });
  },

  /**
   * 显示错误信息
   */
  showError: function(msg) {
    wx.showToast({
      title: msg,
      icon: 'none',
      duration: 2000
    });
  },

  /**
   * 添加图片
   */
  addImage: function() {
    const totalImages = this.data.imageList.length + this.data.newImageList.length;
    if (totalImages >= 6) {
      this.showError('最多只能上传6张图片');
      return;
    }

    // 直接调用拍照功能，不再显示选择菜单
    this.takePhoto();
  },

  /**
   * 拍照上传
   */
  takePhoto: function() {
    const totalImages = this.data.imageList.length + this.data.newImageList.length;
    if (totalImages >= 6) {
      this.showError('最多只能上传6张图片');
      return;
    }

    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['camera'],
      camera: 'back',
      success: async res => {
        const tempFiles = res.tempFiles;
        
        if (tempFiles && tempFiles.length > 0) {
          wx.showLoading({
            title: '处理图片中...',
            mask: true
          });
          
          try {
            // 添加水印
            const watermarkedImages = await imageUtils.addWatermarkToImages([tempFiles[0].tempFilePath]);
            
            // 压缩图片
            const compressedImages = await imageUtils.compressImages(watermarkedImages, 0.8);
            
            // 将新拍的照片添加到newImageList中，包含拍照时间和唯一ID
            const captureTime = new Date();
            const uniqueId = `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
            const newImageItem = {
              url: compressedImages[0], // 新拍的照片
              captureTime: captureTime,
              uniqueId: uniqueId, // 唯一标识符，用于生成唯一文件名
              isNewInCurrentSession: true // 标记为当前编辑会话中新增的图片
            };

            const updatedNewImageList = [...this.data.newImageList, newImageItem];
            this.setData({
              newImageList: updatedNewImageList,
              hasNewPhotos: true, // 标记已拍照上传新照片
              hasTakenNewPhoto: true // 标记已拍摄新照片（用于isTakePhoto字段）
            });
            
            wx.hideLoading();
            
            // 拍照成功后获取高精度位置信息
            this.getHighAccuracyLocation();
          } catch (error) {
            wx.hideLoading();
            console.error('处理图片失败:', error);
            this.showError('处理图片失败，请重试');
          }
        }
      }
    });
  },

  /**
   * 获取高精度位置信息
   */
  getHighAccuracyLocation: function() {
    wx.showLoading({
      title: '获取位置中...',
      mask: true
    });
    
    wx.getLocation({
      type: 'gcj02', // 使用国测局坐标系
      isHighAccuracy: true, // 开启高精度定位
      highAccuracyExpireTime: 3000, // 高精度定位超时时间，单位ms
      success: res => {
        console.log('获取位置成功:', res);
        // 保存位置信息到data中
        this.setData({
          'locationInfo': {
            latitude: res.latitude,
            longitude: res.longitude,
            accuracy: res.accuracy,
            speed: res.speed,
            altitude: res.altitude,
            verticalAccuracy: res.verticalAccuracy,
            horizontalAccuracy: res.horizontalAccuracy
          }
        });
        
        // 显示成功提示
        wx.showToast({
          title: '位置已更新',
          icon: 'success',
          duration: 1500
        });
      },
      fail: err => {
        console.error('获取位置失败:', err);
        wx.showModal({
          title: '位置获取失败',
          content: '无法获取您的当前位置，请确保已授权位置权限并处于网络良好的环境',
          showCancel: false
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  /**
   * 预览图片
   */
  previewImage: function(e) {
    const index = e.currentTarget.dataset.index;
    const images = this.data.imageList;
    
    // 强制刷新当前图片
    this.refreshSingleImage(index);
    
    // 延迟一下再预览，确保图片已刷新
    setTimeout(() => {
      wx.previewImage({
        current: images[index],
        urls: images
      });
    }, 100);
  },

  /**
   * 预览新图片
   */
  previewNewImage: function(e) {
    const index = e.currentTarget.dataset.index;
    const newImages = this.data.newImageList;

    if (!newImages || newImages.length === 0) return;

    // 构建预览图片URL数组
    const urls = newImages.map(item => item.url);

    wx.previewImage({
      current: urls[index],
      urls: urls
    });
  },

  /**
   * 删除新图片
   */
  deleteNewImage: function(e) {
    const index = e.currentTarget.dataset.index;

    wx.showModal({
      title: '确认删除',
      content: '确定要删除这张新图片吗？',
      success: res => {
        if (res.confirm) {
          const newImageList = [...this.data.newImageList];
          // 将要删除的图片保存到deletedNewImages数组中
          const deletedImage = newImageList[index];

          // 只有云存储图片（以cloud://开头）才需要记录以便后续删除
          if (deletedImage && deletedImage.url && deletedImage.url.startsWith('cloud://')) {
            this.setData({
              deletedNewImages: [...this.data.deletedNewImages, deletedImage.url]
            });
          }

          // 从新图片列表中移除
          newImageList.splice(index, 1);

          // 更新新图片列表并重新检查是否有新照片
          this.setData({ newImageList }, () => {
            // 删除图片后重新检查是否还有新拍摄的照片
            this.updateNewPhotosStatus();
          });
        }
      }
    });
  },

  /**
   * 刷新单个图片
   */
  refreshSingleImage: function(index) {
    if (index === undefined || !this.data.imageList[index]) return;
    
    // 获取当前图片URL
    const currentUrl = this.data.imageList[index];
    const imageList = [...this.data.imageList];
    
    // 先设置为空，然后再恢复原URL，强制刷新
    imageList[index] = '';
    
    this.setData({
      imageList: imageList
    }, () => {
      // 短暂延迟后恢复原URL
      setTimeout(() => {
        imageList[index] = currentUrl;
        this.setData({
          imageList: imageList
        });
      }, 50);
    });
  },

  /**
   * 删除图片
   */
  deleteImage: function(e) {
    const index = e.currentTarget.dataset.index;
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这张图片吗？',
      success: res => {
        if (res.confirm) {
          const imageList = [...this.data.imageList];
          // 将要删除的图片保存到deletedImages数组中
          const deletedImage = imageList[index];
          
          // 只有云存储图片（以cloud://开头）才需要记录以便后续删除
          if (deletedImage && deletedImage.startsWith('cloud://')) {
            this.setData({
              deletedImages: [...this.data.deletedImages, deletedImage]
            });
          }
          
          // 从图片列表中移除
          imageList.splice(index, 1);
          
          // 更新图片列表（允许删除所有照片）
          this.setData({ imageList });
        }
      }
    });
  },



  /**
   * 保存修改
   */
  saveChanges: function() {
    // 检查图片数量（包括新旧两种格式）
    const totalImages = this.data.imageList.length + this.data.newImageList.length;
    if (totalImages === 0) {
      this.showError('请至少上传一张图片');
      return;
    }
    
    wx.showModal({
      title: '确认保存',
      content: '确定要保存修改吗？',
      success: res => {
        if (res.confirm) {
          this.submitChanges();
        }
      }
    });
  },

  /**
   * 提交修改
   */
  submitChanges: function() {
    const { postId, imageList, newImageList, formData, postData, locationInfo, deletedImages, deletedNewImages, hasNewPhotos, hasTakenNewPhoto } = this.data;

    if (this.data.submitting) {
      return;
    }

    this.setData({ submitting: true });

    wx.showLoading({
      title: '保存中...',
      mask: true
    });

    // 构建更新数据
    const updateData = {};

    // 检查是否真正有新图片被保存（更准确的判断逻辑）
    const hasActualNewPhotos = this.checkIfHasActualNewPhotos();

    // 只有真正保存了新照片才更新photoUpdated字段
    if (hasActualNewPhotos) {
      updateData.photoUpdated = db.serverDate();
      updateData.status = 'active'; // 如果用户上传过新照片，将状态改为active
      console.log('检测到实际保存的新照片，更新photoUpdated字段并将状态设为active');
    }

    // 只有拍摄了新照片才添加isTakePhoto字段
    if (hasTakenNewPhoto) {
      updateData.isTakePhoto = true;
    }
    
    // 添加位置信息（如果有）- 使用GeoPoint类型
    if (locationInfo && locationInfo.latitude && locationInfo.longitude) {
      // 创建GeoPoint对象
      updateData.location = db.Geo.Point(locationInfo.longitude, locationInfo.latitude);
      // 更新位置获取时间
      updateData.location_updated = db.serverDate();
    }
    
    // 添加表单数据
    // 植物名称
    updateData.title = formData.title;

    // 价格相关
    updateData.price = formData.price !== '' ? parseFloat(formData.price) : 0;

    // 规格参数
    updateData.meter_diameter = formData.trunkDiameter !== '' ? parseFloat(formData.trunkDiameter) : null;
    updateData.thorax_diameter = formData.chestDiameter !== '' ? parseFloat(formData.chestDiameter) : null;
    updateData.ground_diameter = formData.groundDiameter !== '' ? parseFloat(formData.groundDiameter) : null;
    updateData.cup = formData.cupSize !== '' ? parseFloat(formData.cupSize) : null;
    updateData.canopy = formData.crownWidth !== '' ? parseFloat(formData.crownWidth) : null;
    updateData.height = formData.height !== '' ? parseFloat(formData.height) : null;
    updateData.branchPos = formData.branchPoint !== '' ? parseFloat(formData.branchPoint) : null;
    updateData.clumpCount = formData.clumpCount !== '' ? parseFloat(formData.clumpCount) : null;
    updateData.clumpDiameter = formData.clumpDiameter !== '' ? parseFloat(formData.clumpDiameter) : null;

    // 数量和其他
    updateData.quantity = formData.quantity;
    updateData.quality = formData.quality;
    updateData.content = formData.content;
    
    // 保险操作：先上传文件，成功后再删除旧文件和更新数据库
    this.uploadImagesWithSafety(postId, imageList, newImageList, deletedImages, deletedNewImages, updateData)
      .then(res => {
        this.setData({ submitting: false });
        wx.hideLoading();
        
        if (res.result && res.result.code === 0) {
          // 更新成功后，清除所有"新"标签标记
          this.clearNewSessionTags();

          // 强制刷新图片
          this.refreshImages();

          wx.showToast({
            title: '更新成功',
            icon: 'success',
            duration: 2000
          });

          // 返回上一页
          setTimeout(() => {
            wx.navigateBack();
          }, 2000);
        } else {
          console.error('更新失败，服务器返回:', res.result);
          // 显示具体错误信息
          const errorMsg = res.result?.msg || '更新失败，请重试';
          this.showError(errorMsg);
        }
      })
      .catch(err => {
        this.setData({ submitting: false });
        wx.hideLoading();
        console.error('更新失败:', err);

        // 根据错误类型提供不同的用户提示
        let errorMsg = '更新失败，请重试';
        let showRetryOption = true;

        if (err && err.errMsg) {
          if (err.errMsg.includes('uploadFile')) {
            errorMsg = '图片上传失败，请检查网络连接后重试';
          } else if (err.errMsg.includes('callFunction')) {
            errorMsg = '数据保存失败，请重试';
          } else {
            errorMsg = err.errMsg;
          }
        } else if (err && typeof err === 'object') {
          errorMsg = JSON.stringify(err).substring(0, 100);
        } else if (err && typeof err === 'string') {
          errorMsg = err;
        }

        // 显示错误信息，并提供重试选项
        if (showRetryOption) {
          wx.showModal({
            title: '提交失败',
            content: errorMsg + '\n\n是否重试？',
            confirmText: '重试',
            cancelText: '取消',
            success: res => {
              if (res.confirm) {
                // 用户选择重试
                this.submitChanges();
              }
            }
          });
        } else {
          this.showError(errorMsg);
        }
      });
  },

  /**
   * 强制刷新图片
   */
  refreshImages: function() {
    // 获取当前图片列表
    const currentImages = this.data.imageList;
    
    // 先清空图片列表，然后重新设置，强制刷新
    this.setData({ imageList: [] }, () => {
      setTimeout(() => {
        this.setData({ imageList: currentImages });
      }, 50);
    });
  },

  /**
   * 上传图片
   */
  uploadImages: function(postId, imageList) {
    return new Promise(async (resolve, reject) => {
      try {
        const app = getApp();
        const userId = app.globalData.userId || '';
        
        if (!userId) {
          reject(new Error('未获取到用户ID'));
          return;
        }
        
        // 如果没有图片，直接返回空数组
        if (!imageList || imageList.length === 0) {
          resolve([]);
          return;
        }
        
        wx.showLoading({
          title: '上传图片中...',
          mask: true
        });
        
        // 创建文件夹路径 - 与publish.js保持一致
        const folderPath = `supply_images/${userId}_${postId}`;
        
        const uploadTasks = imageList.map((filePath, index) => {
          // 检查是否是已有的云存储图片（以cloud://开头）
          if (filePath.startsWith('cloud://')) {
            return Promise.resolve(filePath);
          }
          
          // 获取文件扩展名
          const extension = filePath.match(/\.([^.]+)$/)?.[1] || 'jpg';
          
          // 生成云存储路径，与publish.js保持一致
          const cloudPath = `${folderPath}/image_${index}.${extension}`;
          
          return wx.cloud.uploadFile({
            cloudPath: cloudPath,
            filePath: filePath
          }).then(res => {
            if (!res.fileID) {
              throw new Error(`老图片上传失败：${cloudPath}`);
            }
            console.log(`老图片上传成功：${cloudPath} -> ${res.fileID}`);
            return res.fileID;
          }).catch(uploadError => {
            console.error(`老图片上传失败：${cloudPath}`, uploadError);
            throw new Error(`老图片上传失败：${cloudPath}，错误：${uploadError.errMsg || uploadError.message}`);
          });
        });
        
        const uploadResults = await Promise.all(uploadTasks);

        wx.hideLoading();
        resolve(uploadResults);
      } catch (error) {
        wx.hideLoading();
        console.error('上传图片失败:', error);
        reject(error);
      }
    });
  },

  /**
   * 数据一致性检查（保险操作）
   */
  checkDataConsistency: function() {
    const { newImageList } = this.data;

    if (!newImageList || newImageList.length === 0) {
      return true; // 没有新图片，无需检查
    }

    let hasInconsistency = false;
    const inconsistentImages = [];

    newImageList.forEach((imageItem, index) => {
      // 检查必要字段
      if (!imageItem.url) {
        hasInconsistency = true;
        inconsistentImages.push(`第${index + 1}张图片缺少URL`);
      } else if (!imageItem.url.startsWith('cloud://') && !imageItem.url.startsWith('http')) {
        hasInconsistency = true;
        inconsistentImages.push(`第${index + 1}张图片URL格式异常`);
      }

      if (!imageItem.captureTime) {
        hasInconsistency = true;
        inconsistentImages.push(`第${index + 1}张图片缺少拍照时间`);
      }
    });

    if (hasInconsistency) {
      console.warn('发现数据不一致:', inconsistentImages);
      wx.showModal({
        title: '数据异常',
        content: '检测到图片数据异常，建议重新拍照上传。\n\n异常详情：\n' + inconsistentImages.join('\n'),
        confirmText: '知道了',
        showCancel: false
      });
      return false;
    }

    return true;
  },

  /**
   * 清除当前会话的"新"标签标记
   */
  clearNewSessionTags: function() {
    const { newImageList } = this.data;

    if (!newImageList || newImageList.length === 0) {
      return;
    }

    // 移除所有图片的isNewInCurrentSession标记
    const updatedNewImageList = newImageList.map(item => {
      const { isNewInCurrentSession, ...itemWithoutNewTag } = item;
      return itemWithoutNewTag;
    });

    this.setData({
      newImageList: updatedNewImageList
    });

    console.log('已清除所有"新"标签标记');
  },



  /**
   * 返回上一页
   */
  goBack: function() {
    // 直接调用onNavBack方法，确保返回逻辑一致
    this.onNavBack();
  },

  /**
   * 导航栏返回按钮点击事件
   */
  onNavBack: function(e) {
    // 检查是否有删除或新增的照片（包括新旧两种格式）
    const hasImageChanges = this.data.deletedImages.length > 0 ||
                           this.data.deletedNewImages.length > 0 ||
                           (this.data.imageList.length !== this.data.originalImageList.length) ||
                           (this.data.newImageList.length !== this.data.originalNewImageList.length);
    
    // 检查是否有修改规格参数
    let hasSpecChanges = false;
    const { specs, postData } = this.data;
    for (const key in specs) {
      if (specs[key].visible && String(specs[key].value) !== String(postData[key] || '')) {
        hasSpecChanges = true;
        break;
      } else if (!specs[key].visible && postData[key]) {
        hasSpecChanges = true;
        break;
      }
    }
    
    // 如果有任何修改，提示用户确认
    if (hasImageChanges || hasSpecChanges) {
      wx.showModal({
        title: '未保存的修改',
        content: '您有未保存的修改，确定要返回吗？',
        success: res => {
          if (res.confirm) {
            wx.navigateBack();
          }
          // 如果用户点击取消，则不执行任何操作
        }
      });
    } else {
      wx.navigateBack();
    }
  },

  /**
   * 导航栏首页按钮点击事件
   */
  onNavHome: function() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    });
  },

  /**
   * 从云存储中删除图片
   */
  deleteCloudImages: function(imageList) {
    // 如果没有要删除的图片，直接返回成功
    if (!imageList || imageList.length === 0) {
      return Promise.resolve();
    }
    
    console.log('删除云存储图片:', imageList);
    
    // 创建删除任务列表
    const deletePromises = imageList.map(fileID => {
      return wx.cloud.deleteFile({
        fileList: [fileID]
      })
      .then(res => {
        console.log('删除云存储图片成功:', res);
        return res;
      })
      .catch(err => {
        console.error('删除云存储图片失败:', err);
        // 即使删除失败，也继续执行流程
        return Promise.resolve();
      });
    });
    
    // 等待所有删除任务完成
    return Promise.all(deletePromises);
  },

  /**
   * 处理图片加载错误
   */
  handleImageError: function(e) {
    const idx = e.currentTarget.dataset.idx;
    if (idx === undefined || !this.data.imageList[idx]) return;

    console.log('图片加载错误，尝试重新加载:', idx);

    // 获取当前图片URL
    const currentUrl = this.data.imageList[idx];

    // 使用延迟重新设置图片URL，强制刷新
    const imageList = [...this.data.imageList];

    // 先设置为空，然后再恢复原URL，强制刷新
    imageList[idx] = '';

    this.setData({
      imageList: imageList
    }, () => {
      // 短暂延迟后恢复原URL
      setTimeout(() => {
        imageList[idx] = currentUrl;
        this.setData({
          imageList: imageList
        });
      }, 100);
    });
  },

  /**
   * 更新新照片状态标志
   */
  updateNewPhotosStatus: function() {
    // 检查当前是否还有在本次编辑会话中新拍摄的照片
    const hasCurrentSessionNewPhotos = this.data.newImageList.some(item =>
      item.isNewInCurrentSession === true
    );

    // 更新hasNewPhotos标志
    this.setData({
      hasNewPhotos: hasCurrentSessionNewPhotos
    });

    console.log('更新新照片状态:', hasCurrentSessionNewPhotos);
  },

  /**
   * 检查是否真正有新图片被保存
   */
  checkIfHasActualNewPhotos: function() {
    const { newImageList, originalNewImageList } = this.data;

    // 方法1: 检查是否有标记为当前会话新增的图片
    const hasNewInCurrentSession = newImageList.some(item =>
      item.isNewInCurrentSession === true
    );

    if (hasNewInCurrentSession) {
      console.log('检测到当前会话新增的图片');
      return true;
    }

    // 方法2: 比较当前newImageList与原始newImageList的数量
    const currentCount = newImageList.length;
    const originalCount = originalNewImageList.length;

    if (currentCount > originalCount) {
      console.log('检测到新图片数量增加:', currentCount, '>', originalCount);
      return true;
    }

    // 方法3: 检查是否有新的uniqueId（新拍摄的照片会有新的uniqueId）
    const originalUniqueIds = new Set(originalNewImageList.map(item => item.uniqueId).filter(Boolean));
    const hasNewUniqueId = newImageList.some(item =>
      item.uniqueId && !originalUniqueIds.has(item.uniqueId)
    );

    if (hasNewUniqueId) {
      console.log('检测到新的uniqueId');
      return true;
    }

    console.log('未检测到实际的新图片');
    return false;
  }
});